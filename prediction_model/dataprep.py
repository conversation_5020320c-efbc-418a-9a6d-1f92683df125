"""
数据预处理模块
用于标准化数据集并生成训练/测试/验证数据
"""
import numpy as np
from sklearn.preprocessing import StandardScaler


def data_preprocess(dataframe, data_cols, split):
    """
    标准化数据集并生成训练/测试/验证数据

    参数:
        dataframe: pandas DataFrame对象，包含原始数据
        data_cols: 要使用的数据列名列表
        split: 3元素向量，和为1，表示测试/训练/验证数据集的比例

    返回:
        data: 包含标准化特征数据的字典
        time: 包含目标时间数据的字典
        train: 训练集索引
        test: 测试集索引
        validation: 验证集索引
        scaler: 标准化器对象
    """
    # 验证分割比例是否正确
    if not sum(split) == 1:
        print("分割比例之和必须为1 (当前为 {:.3f})".format(sum(split)))

    # 目标列名（执行时间的中位数）
    use_target = 'timeUsed_median'

    # 提取特征数据
    df_data = dataframe.loc[:, data_cols].values

    # 创建并拟合标准化器
    scaler = StandardScaler()
    scaler.fit(df_data)

    # 标准化数据
    dataScale = scaler.transform(df_data)

    # 获取数据长度
    dflen = dataScale.shape[0]

    # 计算分割点
    test_split = int(np.floor(split[0] * dflen))                    # 测试集分割点
    validation_split = int(np.floor((split[0] + split[1]) * dflen)) # 验证集分割点

    # 随机打乱索引
    shuffleInd = np.random.permutation(dflen)

    # 分割索引
    train, test, validation = np.split(shuffleInd, [test_split, validation_split])

    # 构建特征数据字典
    data = {}
    data['Train'] = dataScale[train, :]         # 训练集特征
    data['Test'] = dataScale[test, :]           # 测试集特征
    data['Validation'] = dataScale[validation, :] # 验证集特征

    # 构建目标时间数据字典
    time = {}
    time['Train'] = dataframe.values[train, dataframe.columns.get_loc(use_target)].astype(np.float32)
    time['Test'] = dataframe.values[test, dataframe.columns.get_loc(use_target)].astype(np.float32)
    time['Validation'] = dataframe.values[validation, dataframe.columns.get_loc(use_target)].astype(np.float32)

    # 打印数据集大小信息
    print("训练数据集大小: %d \n"
          "测试数据集大小: %d \n"
          "验证数据集大小: %d"
          % (len(time['Train']), len(time['Test']), len(time['Validation'])))

    return data, time, train, test, validation, scaler


def data_preprocess_keep(dataframe, data_cols, split, scaler):
    """
    使用现有标准化器处理数据集并生成训练/测试/验证数据

    参数:
        dataframe: pandas DataFrame对象，包含原始数据
        data_cols: 要使用的数据列名列表
        split: 3元素向量，和为1，表示测试/训练/验证数据集的比例
        scaler: 预训练的标准化器对象

    返回:
        data: 包含标准化特征数据的字典
        time: 包含目标时间数据的字典
        train: 训练集索引
        test: 测试集索引
        validation: 验证集索引
    """
    # 验证分割比例是否正确
    if not sum(split) == 1:
        print("分割比例之和必须为1 (当前为 {:.3f})".format(sum(split)))

    # 目标列名（执行时间的中位数）
    use_target = 'timeUsed_median'

    # 提取特征数据
    df_data = dataframe.loc[:, data_cols]

    # 使用现有标准化器转换数据
    dataScale = scaler.transform(df_data.values)

    # 获取数据长度
    dflen = dataScale.shape[0]

    # 计算分割点
    test_split = int(np.floor(split[0] * dflen))                    # 测试集分割点
    validation_split = int(np.floor((split[0] + split[1]) * dflen)) # 验证集分割点

    # 随机打乱索引
    shuffleInd = np.random.permutation(dflen)

    # 分割索引
    train, test, validation = np.split(shuffleInd, [test_split, validation_split])

    # 构建特征数据字典
    data = {}
    data['Train'] = dataScale[train, :]         # 训练集特征
    data['Test'] = dataScale[test, :]           # 测试集特征
    data['Validation'] = dataScale[validation, :] # 验证集特征

    # 构建目标时间数据字典
    time = {}
    time['Train'] = dataframe.values[train, dataframe.columns.get_loc(use_target)].astype(np.float32)
    time['Test'] = dataframe.values[test, dataframe.columns.get_loc(use_target)].astype(np.float32)
    time['Validation'] = dataframe.values[validation, dataframe.columns.get_loc(use_target)].astype(np.float32)

    # 打印数据集大小信息
    print("训练数据集大小: %d \n"
          "测试数据集大小: %d \n"
          "验证数据集大小: %d"
          % (len(time['Train']), len(time['Test']), len(time['Validation'])))

    return data, time, train, test, validation
