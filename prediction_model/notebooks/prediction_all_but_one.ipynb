#%% md
# Predict GPU performance from other GPUs
### Use model trained on training data set from 5 GPUs, test on (all) data from remaining GPU
#%%
import os
import sys
import tensorflow as tf
import numpy as np
from sklearn.externals import joblib
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
#%%
# Root directory of the project
ROOT_DIR = os.path.abspath("../../")
sys.path.append(ROOT_DIR)  # To find local version of the library
#%%
from prediction_model import dataprep
#%%
predict = 'V100'
data_model = 'only_%s' %predict

prediction_model = 'no_%s' %predict
#%%
# Set paths
PREDICTION_PATH = os.path.join(ROOT_DIR, 'prediction_model/models/%s/saved_model' %prediction_model)
SCALER_PATH = os.path.join(ROOT_DIR, 'prediction_model/models/%s/scaler_Conv.save' %prediction_model)
DATA_PATH = os.path.join(ROOT_DIR, 'prediction_model/data/Data_convolution_%s.pkl' %data_model)
#%%
scaler = joblib.load(SCALER_PATH)
df = pd.read_pickle(DATA_PATH)
tfmodel_file = PREDICTION_PATH
#%%
df.describe()
#%%
data_cols_conv = ['batchsize','elements_matrix','elements_kernel','channels_in','channels_out','padding','strides','use_bias', 
                      'opt_SGD','opt_Adadelta','opt_Adagrad','opt_Momentum','opt_Adam','opt_RMSProp',
                      'act_relu','act_tanh','act_sigmoid',
                      'bandwidth','cores','clock']
split=[0,0,1]
#%%
data, time, train, test, validation = dataprep.data_preprocess_keep(df,data_cols_conv,split,scaler)
#%%
with tf.Session() as sess:
    tf.saved_model.loader.load(sess, ["serve"], tfmodel_file)
    graph = tf.get_default_graph()


    run = sess.run(
            'model_prediction:0',
            feed_dict={'model_input:0': data['Validation'], 
                       'model_targets:0': time['Validation'],
                       'model_istraining:0': False})
RMS_validation_error=np.sqrt(np.mean((time['Validation']-run)**2))
perc_error = np.mean(np.abs(time['Validation']-run)/time['Validation'])*100

fig,ax = plt.subplots(1,1,figsize=[8,8])
ax.plot(time['Validation'],run,'.')


ax.get_xaxis().set_major_formatter(matplotlib.ticker.ScalarFormatter())
ax.get_yaxis().set_major_formatter(matplotlib.ticker.ScalarFormatter())
ax.xaxis.set_minor_formatter(plt.NullFormatter())
ax.yaxis.set_minor_formatter(plt.NullFormatter())

matplotlib.rc('xtick', labelsize=25) 
matplotlib.rc('ytick', labelsize=25) 

plt.xlabel('measured time (ms)',fontsize=25)
plt.ylabel('predicted time (ms)',fontsize=25)


axlim = max(plt.xlim()[1],plt.ylim()[1])


#plt.axis('equal')
ax.set_xlim(-axlim*.025,axlim*1.025)
ax.set_ylim(-axlim*.025,axlim*1.025)

plt.tight_layout()
ax.ticklabel_format(useOffset=False)
ax.plot([0,axlim],[0,axlim],2)

#plt.savefig('figures/prediction_%s_from_other.png' %predict, dpi=300)

plt.show()
#%%
RMS_validation_error
#%%
perc_error