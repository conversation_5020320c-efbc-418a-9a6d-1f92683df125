#%%
import pandas as pd
import numpy as np
import os
#%%
model_name = 'k80'
file_name = 'k80'
#%%
DATA_DIR = os.path.abspath("../../../prediction_model/data/raw_data")
SAVE_DIR = os.path.abspath("../../../prediction_model/data/")
#%% md
## Dense Layer
#%%
dfDense = pd.read_pickle(os.path.join(DATA_DIR,'%s/8/benchmark_dense__20180907.pkl' %file_name))

for i in range(9,10):
    dfDense = pd.concat([dfDense,pd.read_pickle(os.path.join(DATA_DIR,'%s/%d/benchmark_dense__20180907.pkl' %(file_name, i)))])
#%%
ops = (dfDense['batchsize']
       * dfDense['dim_input'] 
       * dfDense['dim_output'])

memory_weights = dfDense['dim_input'] * dfDense['dim_output']
memory_in = dfDense['batchsize'] * dfDense['dim_input']
memory_out = dfDense['batchsize'] * dfDense['dim_output']


dfDense['optimizer'] = dfDense['optimizer'].replace({0:'opt_None',
                                                     1:'opt_SGD',
                                                     2:'opt_Adadelta',
                                                     3:'opt_Adagrad',
                                                     4:'opt_Momentum',
                                                     5:'opt_Adam',
                                                     6:'opt_RMSProp'})

dfDense['activation_fct'] = dfDense['activation_fct'].replace({0:'act_None',
                                                               1:'act_relu',
                                                               2:'act_tanh',
                                                               3:'act_sigmoid'})

dfDense['ops'] = ops
dfDense['memory_weights'] = memory_weights
dfDense['memory_in'] = memory_in
dfDense['memory_out'] = memory_out
#%%
one_hot_optimizer = pd.get_dummies(dfDense['optimizer'])

dfDense = dfDense.drop(labels='optimizer',axis=1)
dfDense = pd.concat([dfDense,one_hot_optimizer],axis=1)


one_hot_activation = pd.get_dummies(dfDense['activation_fct'])

dfDense = dfDense.drop(labels='activation_fct',axis=1)
dfDense = pd.concat([dfDense,one_hot_activation],axis=1)
#%%
dfDense.describe()
#%%
dfDense.to_pickle(os.path.join(SAVE_DIR,'Data_dense_%s.pkl'%model_name))
#%% md
## Convolutional Layers
#%%
dfConv = pd.read_pickle(os.path.join(DATA_DIR,'%s/0/benchmark_convolution__20181031.pkl' %file_name))

header = dfConv.columns

for i in range(1,8):
    dfConv = pd.concat([dfConv,pd.read_pickle(os.path.join(DATA_DIR,'%s/%i/benchmark_convolution__20181031.pkl' %(file_name, i)))])
#%%
padding_reduction = ((dfConv['padding']==0)
                     *(dfConv['kernelsize']-1))

elements_output = ((dfConv['matsize'] - padding_reduction)
                   / dfConv['strides'])**2

ops = (dfConv['batchsize']
       * elements_output
       * dfConv['kernelsize']**2
       * dfConv['channels_in']
       * dfConv['channels_out'])

memory_weights = (dfConv['kernelsize']**2
                  * dfConv['channels_in']
                  * dfConv['channels_out']
                  + dfConv['use_bias'] * dfConv['channels_out'])

memory_in = (dfConv['batchsize']
             * dfConv['matsize']**2
             * dfConv['channels_in'])

memory_out = (dfConv['batchsize']
              * elements_output
              * dfConv['channels_out'])


dfConv['elements_matrix'] = dfConv['matsize']**2
dfConv['elements_kernel'] = dfConv['kernelsize']**2

    
dfConv['ops'] = ops
dfConv['memory_weights'] = memory_weights
dfConv['memory_in'] = memory_in
dfConv['memory_out'] = memory_out


dfConv['optimizer'] = dfConv['optimizer'].replace({0:'opt_None',
                                                   1:'opt_SGD',
                                                   2:'opt_Adadelta',
                                                   3:'opt_Adagrad',
                                                   4:'opt_Momentum',
                                                   5:'opt_Adam',
                                                   6:'opt_RMSProp'})

dfConv['activation_fct'] = dfConv['activation_fct'].replace({0:'act_None',
                                                             1:'act_relu',
                                                             2:'act_tanh',
                                                             3:'act_sigmoid'})

dfConv['use_bias'] = np.uint8(dfConv['use_bias'])


dfConv.dropna(inplace=True)
#%%
one_hot_optimizer = pd.get_dummies(dfConv['optimizer'])

dfConv = dfConv.drop(labels='optimizer',axis=1)
dfConv = pd.concat([dfConv,one_hot_optimizer],axis=1)


one_hot_activation = pd.get_dummies(dfConv['activation_fct'])

dfConv = dfConv.drop(labels='activation_fct',axis=1)
dfConv = pd.concat([dfConv,one_hot_activation],axis=1)
#%%
dfConv.describe()
#%%
dfConv.to_pickle(os.path.join(SAVE_DIR,'Data_convolution_%s.pkl'%model_name))
#%%
