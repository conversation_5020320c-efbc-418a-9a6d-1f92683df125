#%%
import pandas as pd
import numpy as np
import os
#%%
SAVE_DIR = os.path.abspath("../../../prediction_model/data/")
#%% md
## Dense Layer
#%%
dfDense_V100 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_dense_V100.pkl'))
dfDense_V100['peak_performance'] = 14900
dfDense_V100['IO'] = 300
dfDense_V100['bandwidth'] = 900
dfDense_V100['cores'] = 5120
dfDense_V100['clock'] = 1455

dfDense_M60 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_dense_M60.pkl'))
dfDense_M60['peak_performance'] = 7365
dfDense_M60['IO'] = 15.75
dfDense_M60['bandwidth'] = 320
dfDense_M60['cores'] = 4096
dfDense_M60['clock'] = 1178

dfDense_1080ti = pd.read_pickle(os.path.join(SAVE_DIR,'Data_dense_1080ti.pkl'))
dfDense_1080ti['peak_performance'] = 10609 
dfDense_1080ti['IO'] = 15.75
dfDense_1080ti['bandwidth'] = 484
dfDense_1080ti['cores'] = 3584
dfDense_1080ti['clock'] = 1582

dfDense_K40 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_dense_K40.pkl'))
dfDense_K40['peak_performance'] = 4291 
dfDense_K40['IO'] = 15.75
dfDense_K40['bandwidth'] = 288
dfDense_K40['cores'] = 2880
dfDense_K40['clock'] = 875

dfDense_K80 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_dense_K80.pkl'))
dfDense_K80['peak_performance'] = 5591 
dfDense_K80['IO'] = 15.75
dfDense_K80['bandwidth'] = 240
dfDense_K80['cores'] = 4992/2
dfDense_K80['clock'] = 875

dfDense_P100 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_dense_P100.pkl'))
dfDense_P100['peak_performance'] = 8071 
dfDense_P100['IO'] = 15.75
dfDense_P100['bandwidth'] = 732
dfDense_P100['cores'] = 3584
dfDense_P100['clock'] = 1303



dfDense = pd.concat([dfDense_V100,dfDense_P100,dfDense_M60,dfDense_1080ti,dfDense_K40,dfDense_K80])
#%%
dfDense.to_pickle(os.path.join(SAVE_DIR,'Data_dense_all.pkl'))
#%% md
## Convolutional Layers
#%%
dfConv_V100 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_V100.pkl'))
dfConv_V100['peak_performance'] = 14900
dfConv_V100['IO'] = 300
dfConv_V100['bandwidth'] = 900
dfConv_V100['cores'] = 5120
dfConv_V100['clock'] = 1455

dfConv_M60 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_M60.pkl'))
dfConv_M60['peak_performance'] = 7365
dfConv_M60['IO'] = 15.75
dfConv_M60['bandwidth'] = 160
dfConv_M60['cores'] = 4096/2
dfConv_M60['clock'] = 1178

dfConv_1080ti = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_1080ti.pkl'))
dfConv_1080ti['peak_performance'] = 10609 
dfConv_1080ti['IO'] = 15.75
dfConv_1080ti['bandwidth'] = 484
dfConv_1080ti['cores'] = 3584
dfConv_1080ti['clock'] = 1582

dfConv_K40 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_K40.pkl'))
dfConv_K40['peak_performance'] = 4291 
dfConv_K40['IO'] = 15.75
dfConv_K40['bandwidth'] = 288
dfConv_K40['cores'] = 2880
dfConv_K40['clock'] = 875

dfConv_K80 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_K80.pkl'))
dfConv_K80['peak_performance'] = 5591 
dfConv_K80['IO'] = 15.75
dfConv_K80['bandwidth'] = 240
dfConv_K80['cores'] = 4992/2
dfConv_K80['clock'] = 875

dfConv_P100 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_P100.pkl'))
dfConv_P100['peak_performance'] = 8071 
dfConv_P100['IO'] = 15.75
dfConv_P100['bandwidth'] = 732
dfConv_P100['cores'] = 3584
dfConv_P100['clock'] = 1303

dfConv = pd.concat([dfConv_V100,dfConv_M60,dfConv_1080ti,dfConv_K40,dfConv_K80,dfConv_P100])
#%%
dfConv.columns
#%%
dfConv = dfConv[dfConv['batchsize']<33]
#%%
dfConv.describe()
#%%
dfConv.to_pickle(os.path.join(SAVE_DIR,'Data_convolution_all.pkl'))