#%%
import pandas as pd
import numpy as np
import os
#%%
SAVE_DIR = os.path.abspath("../../../prediction_model/data/")
#%%
all_gpu = ['V100', 'P100', 'M60', 'K80', 'K40', '1080ti']
#%% md
## Convolutional Layers
#%%
to_skip = 'K40'
#%%
dfConv_V100 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_V100.pkl'))
dfConv_V100['peak_performance'] = 14900
dfConv_V100['IO'] = 300
dfConv_V100['bandwidth'] = 900
dfConv_V100['cores'] = 5120
dfConv_V100['clock'] = 1455

dfConv_M60 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_M60.pkl'))
dfConv_M60['peak_performance'] = 7365
dfConv_M60['IO'] = 15.75
dfConv_M60['bandwidth'] = 160
dfConv_M60['cores'] = 4096/2
dfConv_M60['clock'] = 1178

dfConv_1080ti = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_1080ti.pkl'))
dfConv_1080ti['peak_performance'] = 10609 
dfConv_1080ti['IO'] = 15.75
dfConv_1080ti['bandwidth'] = 484
dfConv_1080ti['cores'] = 3584
dfConv_1080ti['clock'] = 1582

dfConv_K40 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_K40.pkl'))
dfConv_K40['peak_performance'] = 4291 
dfConv_K40['IO'] = 15.75
dfConv_K40['bandwidth'] = 288
dfConv_K40['cores'] = 2880
dfConv_K40['clock'] = 875

dfConv_K80 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_K80.pkl'))
dfConv_K80['peak_performance'] = 5591 
dfConv_K80['IO'] = 15.75
dfConv_K80['bandwidth'] = 240
dfConv_K80['cores'] = 4992/2
dfConv_K80['clock'] = 875

dfConv_P100 = pd.read_pickle(os.path.join(SAVE_DIR,'Data_convolution_P100.pkl'))
dfConv_P100['peak_performance'] = 8071 
dfConv_P100['IO'] = 15.75
dfConv_P100['bandwidth'] = 732
dfConv_P100['cores'] = 3584
dfConv_P100['clock'] = 1303

dfConv = pd.concat([eval('dfConv_'+gpu) for gpu in all_gpu if not gpu == to_skip])
#%%
dfConv.columns
#%%
dfConv = dfConv[dfConv['batchsize']<33]
#%%
dfConv.describe()
#%%
dfConv.to_pickle(os.path.join(SAVE_DIR,'Data_convolution_no_%s.pkl'%to_skip))
#%%
dfConv_only = eval('dfConv_%s' %to_skip)[eval('dfConv_%s' %to_skip)['batchsize']<33]
#%%
dfConv_only = dfConv_only[dfConv_only['batchsize']<33]
#%%
dfConv_only.to_pickle(os.path.join(SAVE_DIR,'Data_convolution_only_%s.pkl' %to_skip))