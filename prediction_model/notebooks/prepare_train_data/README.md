# Prepare training data

These notebooks read pandas dataframes with raw data as generated by Dockerfiles in *Generate_train_data* from different files and directories and combine them into a single dataframe. Additional secondary features are calculated from the data. Pandas dataframes containing the full information are save as .pkl. These data can be used to train a model using the notebook *model_compTime.ipynb*.

Additionally, data files containing data from all GPUs and data from all but one GPU are created using *merge_data-all.ipynb* and *merge_data-all-but-one.ipynb* to train models on data from multiple GPUs. These data also contain information about the GPU specifications.
