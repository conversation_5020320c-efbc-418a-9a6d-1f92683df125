"""
机器学习性能预测模型
用于预测深度学习操作的执行时间
"""
import tensorflow as tf
import numpy as np
import os

class Model:
    """
    性能预测模型类
    使用全连接神经网络预测机器学习操作的执行时间
    """
    def __init__(self, inputs, targets, learning_rate, reg_constant, dropout_rate,
                 num_neurons, lr_initial, lr_decay_step, batch_size, model_name):
        """
        初始化预测模型

        参数:
            inputs: 输入特征张量
            targets: 目标值张量（执行时间）
            learning_rate: 学习率张量
            reg_constant: 正则化常数
            dropout_rate: Dropout比率
            num_neurons: 每层神经元数量列表
            lr_initial: 初始学习率
            lr_decay_step: 学习率衰减步数
            batch_size: 批处理大小
            model_name: 模型名称
        """
        self.inputs = inputs                    # 输入特征
        self.targets = targets                  # 目标执行时间
        self.learning_rate = learning_rate      # 学习率
        self.reg_constant = reg_constant        # L2正则化常数
        self.dropout_rate = dropout_rate        # Dropout比率
        self.num_layers = len(num_neurons)      # 隐藏层数量
        self.num_neurons = num_neurons          # 每层神经元数量
        self.lr_initial = lr_initial            # 初始学习率
        self.lr_decay_step = lr_decay_step      # 学习率衰减步数
        self.batch_size = batch_size            # 批处理大小
        self.model_name = model_name            # 模型名称

        # 延迟初始化的属性
        self._prediction = None                 # 预测结果
        self._loss = None                      # 损失函数
        self._train_op = None                  # 训练操作
        self._summary_op = None                # 摘要操作

        # 训练状态占位符（用于控制Dropout）
        self.istraining = tf.placeholder(tf.bool,
                                         shape=None,
                                         name='model_istraining')

        # 全局步数变量
        self.global_step = tf.get_variable('global_step',
                                           initializer=tf.constant(0),
                                           trainable=False)


    @property
    def prediction(self):
        """
        构建模型并生成预测结果

        返回:
            预测的执行时间张量
        """
        if self._prediction is None:
            data_dim = self.inputs.shape[1]  # 输入特征维度

            fcLayer = self.inputs

            # 构建多个全连接隐藏层
            for layer in range(0, self.num_layers):
                name_scope = 'layer_fc%d' % layer
                with tf.variable_scope(name_scope) as scope:
                    fcLayer = tf.layers.dense(
                            inputs=fcLayer,                     # 输入
                            units=self.num_neurons[layer],     # 神经元数量
                            activation=tf.nn.relu,             # ReLU激活函数
                            kernel_regularizer=tf.contrib.layers.l2_regularizer(self.reg_constant),  # L2正则化
                            use_bias=True)                     # 使用偏置

            # 仅在最后一层后应用Dropout
            fcLayer = tf.layers.dropout(inputs=fcLayer,
                                        rate=self.dropout_rate,     # Dropout比率
                                        training=self.istraining)   # 训练状态控制

            # 输出层：预测执行时间（使用ReLU防止负值）
            output = tf.layers.dense(
                    inputs=fcLayer,         # 输入
                    units=1,               # 单个输出（执行时间）
                    activation=tf.nn.relu, # ReLU激活（确保非负）
                    use_bias=False)        # 不使用偏置

            # 重塑输出为一维张量
            self._prediction = tf.reshape(output, [-1], name='model_prediction')
        return self._prediction


    @property
    def loss(self):
        """
        生成损失函数

        返回:
            损失值张量（正则化损失 + 均方误差）
        """
        if self._loss is None:
            # 组合损失：正则化损失 + 对数空间的均方误差
            self._loss = (tf.losses.get_regularization_loss()           # L2正则化损失
                         + tf.losses.mean_squared_error(
                               labels=tf.log(1+self.targets),          # 对数变换的真实值
                               predictions=tf.log(1+self.prediction))) # 对数变换的预测值

            # 备选方案：直接使用原始值的均方误差（已注释）
            # self._loss = (tf.losses.get_regularization_loss()
            #               + tf.losses.mean_squared_error(
            #                     labels=self.targets,
            #                     predictions=self.prediction))
        return self._loss

    @property
    def train_op(self):
        """
        生成训练操作

        返回:
            训练操作张量
        """
        if self._train_op is None:
            # 使用Adam优化器
            opt = tf.train.AdamOptimizer(
                    learning_rate=self.learning_rate,  # 学习率
                    epsilon=.1)                        # 数值稳定性参数

            # 最小化损失函数
            self._train_op = opt.minimize(
                    self.loss,                         # 要最小化的损失
                    global_step=self.global_step)      # 全局步数计数器
        return self._train_op

    @property
    def summary_op(self):
        """
        生成TensorBoard摘要操作

        返回:
            合并的摘要操作
        """
        if self._summary_op is None:
            # 添加标量摘要
            tf.summary.scalar("loss", self.loss)                    # 损失值
            tf.summary.scalar("learning_rate", self.learning_rate)  # 学习率
            # 添加直方图摘要
            tf.summary.histogram("histogram_loss", self.loss)       # 损失分布
            # 合并所有摘要
            self._summary_op = tf.summary.merge_all()
        return self._summary_op


    def train(self, traindata, trainlabel, testdata, testlabel, num_train_steps):
        """Train the model for a number of steps, save checkpoints, write
        graph and loss for tensorboard
        Inputs:
            traindata
            trainlabel
            testdata
            testlabel
            num_train_steps
        """

        print(os.path.abspath('./'))
        saver = tf.train.Saver()

        initial_step = 0

        try:
            os.mkdir('./checkpoints/%s' %self.model_name)
        except:
            pass

        num_datapoints = traindata.shape[0]
        list_datapoints = np.arange(0,num_datapoints)
        num_batches = np.int(np.ceil(num_datapoints/self.batch_size))

        with tf.Session() as sess:
            sess.run(tf.global_variables_initializer())
            ckpt = tf.train.get_checkpoint_state(os.path.dirname('./checkpoints/%s/checkpoint' %self.model_name))

            # if that checkpoint exists, restore from checkpoint
            if ckpt and ckpt.model_checkpoint_path:
                saver.restore(sess, ckpt.model_checkpoint_path)

            writer_train = tf.summary.FileWriter('./graphs/prediction/train/%s' %self.model_name, sess.graph)
            writer_test = tf.summary.FileWriter('./graphs/prediction/test/%s' %self.model_name, sess.graph)

            initial_step = self.global_step.eval()

            for epoch in range(initial_step, initial_step + num_train_steps):
                np.random.shuffle(list_datapoints)
                avg_loss = 0
                for i in range(0,num_batches):
                    batch = list_datapoints[i*self.batch_size:min((i+1)*self.batch_size,num_datapoints)]
                    _, loss, summary = sess.run(
                            [self.train_op, self.loss, self.summary_op],
                            feed_dict={
                                    self.inputs: traindata[batch,:],
                                    self.targets: trainlabel[batch],
                                    self.learning_rate: self.lr_initial*2**(-np.floor(epoch/self.lr_decay_step)),
                                    self.istraining: True})
                    avg_loss += loss/num_batches
                writer_train.add_summary(summary, global_step=epoch)

                testloss, testsummary = sess.run(
                        [self.loss,self.summary_op],
                        feed_dict={
                                self.inputs: testdata,
                                self.targets: testlabel,
                                self.learning_rate: self.lr_initial*2**(-np.floor(i/self.lr_decay_step)),
                                self.istraining: False})
                writer_test.add_summary(testsummary, global_step=epoch)
                saver.save(sess, './checkpoints/%s/prediction' %self.model_name, epoch)
                if epoch%10==0:
                    print('Epoch {}: Train loss {:.3f}, Test loss {:.3f}'.format(epoch, avg_loss, testloss))


            writer_train.close()
            writer_test.close()
