"""
卷积层基准测试模块
用于生成训练数据的卷积操作基准测试
"""

import tensorflow as tf
import numpy as np


class convolution(object):
    """
    卷积基准测试类
    用于生成具有各种参数配置的卷积操作基准测试
    """

    def __init__(self,
                 batchsize,         # 批处理大小
                 matsize,           # 输入特征图大小
                 kernelsize,        # 卷积核大小
                 channels_in,       # 输入通道数
                 channels_out,      # 输出通道数
                 strides,           # 步长
                 precision,         # 浮点精度
                 padding,           # 填充方式
                 activation_fct,    # 激活函数
                 use_bias,          # 是否使用偏置
                 devlist,           # 设备列表
                 optimizer):        # 优化器
        """
        初始化卷积基准测试对象

        参数:
            batchsize: 批处理大小
            matsize: 输入特征图大小（正方形）
            kernelsize: 卷积核大小
            channels_in: 输入通道数
            channels_out: 输出通道数
            strides: 卷积步长
            precision: 浮点精度（16或32位）
            padding: 填充方式（SAME或VALID）
            activation_fct: 激活函数
            use_bias: 是否使用偏置项
            devlist: GPU/CPU设备列表
            optimizer: 优化器类型
        """
        self.matsize = matsize                  # 输入特征图大小
        self.kernelsize = kernelsize            # 卷积核大小
        self.channels_in = channels_in          # 输入通道数
        self.channels_out = channels_out        # 输出通道数
        self.strides = strides                  # 卷积步长
        self.batchsize = batchsize              # 批处理大小
        self.padding = padding                  # 填充方式
        self.use_bias = use_bias                # 是否使用偏置
        self.precision = precision              # 浮点精度
        self.devlist = devlist                  # 设备列表
        self.activation_fct = activation_fct    # 激活函数
        self.opt = optimizer                    # 优化器


    def create_benchmark_op(self):
        """Create benchmark operation using tf.layer

        Returns:
            conv.op: Operation for convolution
            g: TensorFlow graph
        """

        datatype = eval('tf.float%d' %(self.precision))
        act = eval(self.activation_fct)

        g = tf.Graph()
        run_metadata = tf.RunMetadata()
        with g.as_default():
            for dev in self.devlist:
                with tf.device(dev):
                    matA = tf.Variable(
                            tf.ones([
                                    self.batchsize,
                                    self.matsize,
                                    self.matsize,
                                    self.channels_in],
                            dtype=datatype))
                    conv = tf.layers.conv2d(
                            inputs=matA,
                            filters=self.channels_out,
                            kernel_size=[self.kernelsize,self.kernelsize],
                            strides=self.strides,
                            padding=self.padding,
                            activation = act,
                            use_bias=self.use_bias)

        return conv.op, g


    def create_benchmark_op_with_backprop(self):
        """Create benchmark operation using tf.layer

        Returns:
            conv.op: Operation for convolution
            g: TensorFlow graph
        """

        datatype = eval('tf.float%d' %(self.precision))
        opt = eval('tf.train.%s' % self.opt)
        act = eval(self.activation_fct)

        if self.padding == 'SAME':
            target_size = np.ceil(np.float(self.matsize)/self.strides)
        else:
            target_size = np.ceil(np.float((self.matsize-(self.kernelsize-1)))/self.strides)

        g = tf.Graph()
        run_metadata = tf.RunMetadata()
        with g.as_default():
            for dev in self.devlist:
                with tf.device(dev):
                    matA = tf.Variable(
                            tf.ones([
                                    self.batchsize,
                                    self.matsize,
                                    self.matsize,
                                    self.channels_in],
                            dtype=datatype))

                    target = tf.Variable(
                            tf.ones([
                                    self.batchsize,
                                    target_size,
                                    target_size,
                                    self.channels_out],
                            dtype=datatype))

                    conv = tf.layers.conv2d(
                            inputs=matA,
                            filters=self.channels_out,
                            kernel_size=[self.kernelsize,self.kernelsize],
                            strides=self.strides,
                            padding=self.padding,
                            activation = act,
                            use_bias=self.use_bias)

                    loss = tf.reduce_mean( tf.square( conv - target ) )
                    train_op = opt.minimize(loss=loss)

        return train_op, g
