"""
训练数据生成器
为卷积和全连接层生成具有不同随机参数的基准测试数据
将结果保存为pandas数据框(.pkl)和numpy数组(.npy)
"""
import os
import argparse
import tensorflow as tf
import numpy as np
import pandas as pd
import scipy.stats as stats
import time
import benchmark_conv, benchmark_dense
import run_benchmark

# 设置TensorFlow日志级别
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=全部信息, 1=过滤INFO, 2=过滤WARNING, 3=过滤ERROR

# 创建命令行参数解析器
parser = argparse.ArgumentParser('卷积和全连接层基准测试数据生成器')

# ==================== 基准测试类型选择 ====================
parser.add_argument('--testDense', action="store_true", default=False,
                    help='基准测试全连接层/矩阵乘法')
parser.add_argument('--testConv', action="store_true", default=False,
                    help='基准测试2D卷积')

# ==================== 通用参数设置 ====================
parser.add_argument('--backprop_ratio', type=float, default=0.5,
                    help='包含反向传播的迭代比例 ([0..1])')
parser.add_argument('--num_gpu', type=int, default=1,
                    help='使用的GPU数量')
parser.add_argument('--devlist', type=str, default='',
                    help='设备列表，如果设置则覆盖num_gpu参数')
parser.add_argument('--num_val', type=int, default=100000,
                    help='要计算的结果数量')
parser.add_argument('--logfile', type=str, default='',
                    help='存储结果的文本文件')
parser.add_argument('--device', type=str, default='',
                    help='在日志文件中显示的设备名称')
parser.add_argument('--iter_benchmark', type=int, default=50,
                    help='基准测试的迭代次数')
parser.add_argument('--iter_warmup', type=int, default=5,
                    help='预热阶段的迭代次数')
parser.add_argument('--repetitions', type=int, default=5,
                    help='同一实验的重复次数')

# 解析命令行参数
args = parser.parse_args()


def generate_devlist(devlist, num_gpu):
    """
    创建设备列表

    参数:
        devlist: 逗号分隔的设备列表，会覆盖num_gpu参数
        num_gpu: 要使用的GPU数量

    返回:
        devlist: 设备列表
        use_gpu: 是否使用GPU（布尔值）
    """
    if devlist == '':
        if num_gpu == 0:
            # 如果不使用GPU，则使用CPU
            devlist = ['/cpu:0']
            use_gpu = False
        else:
            # 生成GPU设备列表
            devlist = ['/gpu:%d' % i for i in range(num_gpu)]
            use_gpu = True
    else:
        # 解析自定义设备列表
        use_gpu = ('gpu' in devlist.lower())
        devlist = devlist.split(',')
    return devlist, use_gpu


def main(_):
    """
    主函数：运行所有基准测试并生成训练数据
    """
    # 生成设备列表
    devlist, use_gpu = generate_devlist(args.devlist, args.num_gpu)

    # 定义优化器列表（用于随机选择）
    optimizer_list = [
            'None',                                                    # 0: 无优化器（仅前向传播）
            'GradientDescentOptimizer(learning_rate=0.0001)',         # 1: 随机梯度下降
            'AdadeltaOptimizer(learning_rate=0.0001)',                # 2: Adadelta优化器
            'AdagradOptimizer(learning_rate=0.0001)',                 # 3: Adagrad优化器
            'MomentumOptimizer(learning_rate=0.0001,momentum=0.1)',   # 4: 动量优化器
            'AdamOptimizer(learning_rate=0.0001)',                    # 5: Adam优化器
            'RMSPropOptimizer(learning_rate=0.0001)']                 # 6: RMSprop优化器

    # 定义激活函数列表（用于随机选择）
    activation_list = [
            'None',         # 0: 无激活函数
            'tf.nn.relu',   # 1: ReLU激活函数
            'tf.nn.tanh',   # 2: Tanh激活函数
            'tf.nn.sigmoid'] # 3: Sigmoid激活函数


    # ==================== 卷积基准测试 ====================
    if args.testConv:
        # 设置日志文件路径
        if args.logfile == '':
                logfile = str('/results/benchmark_convolution_%s_%s'
                        % (args.device, time.strftime("%Y%m%d")))
        else:
            logfile = args.logfile

        # ==================== 生成随机参数 ====================
        print("生成卷积层随机参数...")

        # 基本参数随机生成
        batchsize = np.random.randint(1, 65, args.num_val)          # 批处理大小: 1-64
        matsize = np.random.randint(1, 513, args.num_val)           # 输入特征图大小: 1-512
        kernelsize = np.zeros(args.num_val, dtype=np.int32)         # 卷积核大小（稍后设置）
        channels_in = np.zeros(args.num_val, dtype=np.int32)        # 输入通道数（稍后设置）
        channels_out = np.zeros(args.num_val, dtype=np.int32)       # 输出通道数（稍后设置）
        strides = np.random.randint(1, 5, args.num_val)             # 步长: 1-4
        optimizer = np.zeros(args.num_val, dtype=np.int32)          # 优化器索引（稍后设置）
        precision = (np.ones(args.num_val) * 32).astype(int)        # 精度: 固定32位浮点
        padding = np.random.randint(0, 2, args.num_val)             # 填充方式: 0=VALID, 1=SAME
        activation_fct = np.random.randint(0, 4, args.num_val)      # 激活函数索引: 0-3
        use_bias = np.random.choice([True, False], args.num_val)    # 是否使用偏置

        # GPU分配索引（循环分配到不同GPU）
        gpu_index = np.arange(args.num_val) % (len(devlist))

        # 初始化时间记录数组（样本数 x 重复次数）
        timeUsed = np.zeros([args.num_val, args.repetitions])

        # 记录开始时间
        tprint = time.time()

        # 为每个样本设置依赖性参数
        print("设置依赖性参数...")
        for i in range(args.num_val):
            # 卷积核大小不能超过输入大小，最大为6
            kernelsize[i] = np.random.randint(1, min(7, matsize[i]) + 1)

            # 通道数受内存限制，避免过大的张量
            max_channels = int(10000 / matsize[i])
            channels_in[i] = np.random.randint(1, max_channels)
            channels_out[i] = np.random.randint(1, max_channels)

            # 根据反向传播比例决定是否使用优化器
            if np.random.rand() <= args.backprop_ratio:
                # 使用优化器（包含反向传播）
                optimizer[i] = np.random.randint(1, len(optimizer_list))
            else:
                # 不使用优化器（仅前向传播）
                optimizer[i] = 0

        # ==================== 执行基准测试 ====================
        print("开始执行卷积基准测试...")
        for rep in range(args.repetitions):
            print("卷积基准测试，开始第 %d 轮重复" % (rep + 1))

            for i in range(args.num_val):
                # 创建卷积基准测试对象
                conv = benchmark_conv.convolution(
                        batchsize[i],                                           # 批处理大小
                        matsize[i],                                             # 输入特征图大小
                        kernelsize[i],                                          # 卷积核大小
                        channels_in[i],                                         # 输入通道数
                        channels_out[i],                                        # 输出通道数
                        strides[i],                                             # 步长
                        precision[i],                                           # 精度
                        ('SAME' if padding[i] == 1 else 'VALID'),             # 填充方式
                        activation_list[activation_fct[i]],                     # 激活函数
                        use_bias[i],                                            # 是否使用偏置
                        devlist,                                                # 设备列表
                        optimizer_list[optimizer[i]])                          # 优化器

                # 根据是否使用优化器创建不同的基准测试操作
                if optimizer[i] == 0:
                    # 仅前向传播
                    benchmark_op, benchmark_graph = conv.create_benchmark_op()
                else:
                    # 包含反向传播
                    benchmark_op, benchmark_graph = conv.create_benchmark_op_with_backprop()

                # 创建基准测试运行器
                bm_conv = run_benchmark.benchmark(
                        benchmark_op,           # 要执行的操作
                        args.iter_warmup,       # 预热迭代次数
                        args.iter_benchmark,    # 基准测试迭代次数
                        benchmark_graph)        # TensorFlow计算图

                # 执行基准测试并记录时间
                try:
                    timeUsed[i, rep] = bm_conv.run_benchmark()
                except:
                    print('错误: GPU内存不足')
                    timeUsed[i, rep] = None

                # 每100个样本打印一次进度
                if (i + 1) % 100 == 0:
                    print("第 %d/%d 轮: 完成卷积 %d/%d "
                            "(%.2f 秒): t = %.3f ms \n"
                            % (rep + 1,
                            args.repetitions,
                            i + 1,
                            args.num_val,
                            time.time() - tprint,
                            timeUsed[i, rep]))

        # ==================== 生成数据框并保存结果 ====================
        print("生成数据框并保存卷积基准测试结果...")
        df_results = pd.DataFrame({
                'batchsize': batchsize,                                 # 批处理大小
                'matsize': matsize,                                     # 输入特征图大小
                'kernelsize': kernelsize,                               # 卷积核大小
                'channels_in': channels_in,                             # 输入通道数
                'channels_out': channels_out,                           # 输出通道数
                'strides': strides,                                     # 步长
                'padding': padding,                                     # 填充方式
                'precision': precision,                                 # 精度
                'activation_fct': activation_fct,                       # 激活函数索引
                'use_bias': use_bias,                                   # 是否使用偏置
                'optimizer': optimizer,                                 # 优化器索引
                'gpu': gpu_index,                                       # GPU索引
                'timeUsed_median': np.median(timeUsed, 1),             # 执行时间中位数
                'timeUsed_min': np.min(timeUsed, 1),                   # 执行时间最小值
                'timeUsed_max': np.max(timeUsed, 1),                   # 执行时间最大值
                'timeUsed_std': np.std(timeUsed, 1)})                  # 执行时间标准差

        # 保存为pickle文件和numpy数组
        df_results.to_pickle('%s.pkl' % logfile)
        np.save('%s.npy' % logfile, timeUsed)
        print("卷积基准测试结果已保存到: %s" % logfile)


    # ==================== 全连接层基准测试 ====================
    if args.testDense:
        # 设置日志文件路径
        if args.logfile == '':
                logfile = str('/results/benchmark_dense_%s_%s'
                        % (args.device, time.strftime("%Y%m%d")))
        else:
            logfile = args.logfile

        # ==================== 生成随机参数 ====================
        print("生成全连接层随机参数...")

        # 基本参数随机生成
        batchsize = np.random.randint(1, 65, args.num_val)             # 批处理大小: 1-64
        dim_input = np.random.randint(1, 4096, args.num_val)           # 输入维度: 1-4095
        dim_output = np.random.randint(1, 4096, args.num_val)          # 输出维度: 1-4095
        precision = (np.ones(args.num_val) * 32).astype(int)           # 精度: 固定32位浮点
        activation_fct = np.random.randint(0, 4, args.num_val)         # 激活函数索引: 0-3
        optimizer = np.zeros(args.num_val, dtype=np.int32)             # 优化器索引（稍后设置）

        # GPU分配索引（循环分配到不同GPU）
        gpu_index = np.arange(args.num_val) % (len(devlist))

        # 初始化时间记录数组（样本数 x 重复次数）
        timeUsed = np.zeros([args.num_val, args.repetitions])

        # 记录开始时间
        tprint = time.time()

        # 为每个样本设置优化器
        print("设置优化器参数...")
        for i in range(args.num_val):
            # 根据反向传播比例决定是否使用优化器
            if np.random.rand() <= args.backprop_ratio:
                # 使用优化器（包含反向传播）
                optimizer[i] = np.random.randint(1, len(optimizer_list))
            else:
                # 不使用优化器（仅前向传播）
                optimizer[i] = 0

        # ==================== 执行基准测试 ====================
        print("开始执行全连接层基准测试...")
        for rep in range(args.repetitions):
            print("全连接层基准测试，开始第 %d 轮重复" % (rep + 1))

            for i in range(args.num_val):
                # 创建全连接层基准测试对象
                dense = benchmark_dense.dense_layer(
                        dim_input[i],                                   # 输入维度
                        dim_output[i],                                  # 输出维度
                        batchsize[i],                                   # 批处理大小
                        precision[i],                                   # 精度
                        activation_list[activation_fct[i]],             # 激活函数
                        optimizer_list[optimizer[i]],                   # 优化器
                        devlist)                                        # 设备列表

                # 根据是否使用优化器创建不同的基准测试操作
                if optimizer[i] == 0:
                    # 仅前向传播
                    benchmark_op, benchmark_graph = dense.create_benchmark_op()
                else:
                    # 包含反向传播
                    benchmark_op, benchmark_graph = dense.create_benchmark_op_with_backprop()

                # 创建基准测试运行器
                bm_dense = run_benchmark.benchmark(
                        benchmark_op,           # 要执行的操作
                        args.iter_warmup,       # 预热迭代次数
                        args.iter_benchmark,    # 基准测试迭代次数
                        benchmark_graph)        # TensorFlow计算图

                # 执行基准测试并记录时间
                try:
                    timeUsed[i, rep] = bm_dense.run_benchmark()
                except:
                    print('错误: GPU内存不足')
                    timeUsed[i, rep] = None

                # 每100个样本打印一次进度
                if (i + 1) % 100 == 0:
                    print("第 %d/%d 轮: 完成全连接层 %d/%d "
                            "(%.2f 秒): t = %.3f ms \n"
                            % (rep + 1,
                            args.repetitions,
                            i + 1,
                            args.num_val,
                            time.time() - tprint,
                            timeUsed[i, rep]))

        # ==================== 生成数据框并保存结果 ====================
        print("生成数据框并保存全连接层基准测试结果...")
        df_results = pd.DataFrame({
                'batchsize': batchsize,                                 # 批处理大小
                'dim_input': dim_input,                                 # 输入维度
                'dim_output': dim_output,                               # 输出维度
                'precision': precision,                                 # 精度
                'activation_fct': activation_fct,                       # 激活函数索引
                'optimizer': optimizer,                                 # 优化器索引
                'gpu': gpu_index,                                       # GPU索引
                'timeUsed_median': np.median(timeUsed, 1),             # 执行时间中位数
                'timeUsed_min': np.min(timeUsed, 1),                   # 执行时间最小值
                'timeUsed_max': np.max(timeUsed, 1),                   # 执行时间最大值
                'timeUsed_std': np.std(timeUsed, 1)})                  # 执行时间标准差

        # 保存为pickle文件和numpy数组
        df_results.to_pickle('%s.pkl' % logfile)
        np.save('%s.npy' % logfile, timeUsed)
        print("全连接层基准测试结果已保存到: %s" % logfile)


# ==================== 程序入口 ====================
if __name__ == '__main__':
    # 使用TensorFlow的应用运行框架启动主函数
    tf.app.run()
