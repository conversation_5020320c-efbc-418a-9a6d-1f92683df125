# ML performance prediction

### Data generation
In *Generate_train_data* the code to generate benchmarks for convolutional and fully connected layers with random parameters can be found. The Dockerfiles *Dockerfile.dense* and *Dockerfile.conv* can be used to generate training data for dense/fully connected and convolutional layers, respectively. They can be build and run using NVIDIA-Docker. Mount a directory into /results using the flag
```
--mount type=bind,source=<path/to/folder>,target=/results
```
for saving results as pandas dataframes.


### Model training
The data generated by the programme described above is to build a feed-forward model for predicting compute times for convolutions and fully connected layers. The data is preprocessed using the jupyter notebooks in *notebooks/prepare_train_data*. The model can be trained using the jupyter notebook *notebooks/model_compTime.ipynb*.

The resulting model can be used to infer the performance of a wide range of deep learning models on different hardware. Install the python package from https://github.com/CDECatapult/mlpredict for a full pipeline.
