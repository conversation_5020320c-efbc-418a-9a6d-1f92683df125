"""
机器学习基准测试脚本
用于测试常用机器学习操作和VGG16网络的性能

主要功能：
1. 矩阵乘法基准测试
2. 2D卷积基准测试
3. VGG16网络训练基准测试

使用TensorFlow和Keras框架
"""
import os
import argparse
import tensorflow as tf
import time
from utils_tf import benchmark_matmul, benchmark_conv, benchmark_conv_mult, benchmark_VGG16
from utils_tf import run_benchmark

# 设置TensorFlow日志级别，减少输出信息
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=全部信息, 1=过滤INFO, 2=过滤WARNING, 3=过滤ERROR

# 创建命令行参数解析器
parser = argparse.ArgumentParser('机器学习算法各方面性能基准测试工具')

# ==================== 基准测试类型选择 ====================
parser.add_argument('--testMatMul', action="store_true", default=False,
                   help='执行矩阵乘法基准测试')
parser.add_argument('--testConv', action="store_true", default=False,
                   help='执行2D卷积基准测试')
parser.add_argument('--testVGG16', action="store_true", default=False,
                   help='执行VGG-16网络在合成数据上的训练基准测试')

# ==================== 通用参数设置 ====================
parser.add_argument('--num_gpu', type=int, default=1,
                   help='使用的GPU数量')
parser.add_argument('--devlist', type=str, default='',
                   help='设备列表，如果设置则覆盖num_gpu参数')
parser.add_argument('--precision', type=int, default=32,
                   help='浮点数精度（16或32位）')
parser.add_argument('--logfile', type=str, default='',
                   help='存储结果的文本文件路径')
parser.add_argument('--device', type=str, default='',
                   help='在日志文件中显示的设备名称')
parser.add_argument('--use_tf_profiler', action="store_true", default=False,
                   help='使用TensorFlow分析器计算FLOP数量')
parser.add_argument('--iter_benchmark', type=int, default=200,
                   help='基准测试的迭代次数')
parser.add_argument('--iter_timeline', type=int, default=10,
                   help='生成时间线的迭代次数')
parser.add_argument('--iter_warmup', type=int, default=10,
                   help='预热阶段的迭代次数')
parser.add_argument('--iter_internal', type=int, default=10,
                   help='不传输数据的内部迭代次数')

parser.add_argument('--batchsize', type=int, default=128,
                   help='卷积/CNN训练的批处理大小')
parser.add_argument('--no_saving', action="store_true", default=False,
                   help='不保存基准测试结果到CSV文件')
parser.add_argument('--no_timeline', action="store_true", default=False,
                   help='不生成TensorFlow时间线')
parser.add_argument('--comment', type=str, default='',
                   help='在日志文件中添加的注释')

# ==================== 矩阵乘法/2D卷积参数 ====================
parser.add_argument('--logFLOPs', type=int, default=0,
                   help='要执行的FLOP数量的log10值（向上取整），覆盖iter_benchmark，设为0则使用iter')
parser.add_argument('--matsize', type=int, default=1024,
                   help='基准测试中每个矩阵的大小')
parser.add_argument('--kernelsize', type=int, default=3,
                   help='卷积基准测试的卷积核大小')
parser.add_argument('--channels_in', type=int, default=1,
                   help='卷积输入的通道数')
parser.add_argument('--channels_out', type=int, default=1,
                   help='卷积输出的特征数')
parser.add_argument('--padding', type=str, default='SAME',
                   help='卷积层的填充方式（SAME或VALID）')
parser.add_argument('--use_tf_layers', action="store_true", default=False,
                   help='使用tf.layer进行卷积')

# ==================== CNN网络参数 ====================
parser.add_argument('--imgsize', type=int, default=224,
                   help='（正方形）图像的大小')
parser.add_argument('--numclasses', type=int, default=1000,
                   help='图像类别数量')
parser.add_argument('--optimizer', type=str, default='sgd',
                   help='VGG-16使用的优化器（sgd或rmsprop）')

# 解析命令行参数
args = parser.parse_args()


def generate_devlist(devlist, num_gpu):
    """
    创建设备列表

    参数:
        devlist: 逗号分隔的设备列表，会覆盖num_gpu参数
        num_gpu: 要使用的GPU数量

    返回:
        devlist: 设备列表
        use_gpu: 是否使用GPU（布尔值）
    """
    if devlist == '':
        if num_gpu == 0:
            # 如果不使用GPU，则使用CPU
            devlist = ['/cpu:0']
            use_gpu = False
        else:
            # 生成GPU设备列表
            devlist = ['/gpu:%d' % i for i in range(num_gpu)]
            use_gpu = True
    else:
        # 解析自定义设备列表
        use_gpu = ('gpu' in devlist.lower())
        devlist = devlist.split(',')
    return devlist, use_gpu


def main(_):
    """
    主函数：运行所有基准测试
    """
    # 生成设备列表
    devlist, use_gpu = generate_devlist(args.devlist, args.num_gpu)


    # ==================== 矩阵-矩阵乘法基准测试 ====================
    if args.testMatMul:
        # 设置日志文件路径
        if args.logfile == '':
            logfile = str('/results/benchmark_matmul_%s_%s'
                    % (args.device, time.strftime("%Y%m%d")))
        else:
            logfile = args.logfile

        # 计算总操作数（FLOP）
        ops = (args.matsize**3 + (args.matsize)*args.matsize**2)
        # matsize^3 次乘法运算
        # (matsize-1)*matsize^2 次加法运算

        # 创建矩阵乘法基准测试对象
        gemm = benchmark_matmul.gemm(args, devlist)

        # 创建基准测试操作和计算图
        gemm_op, gemm_graph = gemm.create_benchmark_op()

        # 创建基准测试运行器
        bm_matmul = run_benchmark.benchmark(
                gemm_op,                    # 要执行的操作
                args.iter_warmup,           # 预热迭代次数
                args.iter_benchmark,        # 基准测试迭代次数
                args.iter_timeline,         # 时间线迭代次数
                gemm_graph)                 # TensorFlow计算图

        print("========================================\n")
        print("开始矩阵乘法基准测试")

        # 运行基准测试并获取执行时间
        timeUsed = bm_matmul.run_benchmark()

        # 打印测试结果
        print("\n%d x %d 矩阵乘法 (float%d): "
                "%.3f ms, %.3f GFLOPS (%.2f 矩阵/秒)"
                % (args.matsize,
                args.matsize,
                args.precision,
                timeUsed*1000,              # 转换为毫秒
                ops*1e-9/timeUsed,          # 计算GFLOPS
                1/timeUsed))                # 计算每秒处理矩阵数

        # 保存测试结果到CSV文件
        if not args.no_saving:
            # 如果CSV文件不存在，创建并写入表头
            if not os.path.isfile('%s.csv' % logfile):
                header = ('operation, matsize, precision (bits), '
                        'performance (GFLOPs/sec), memory use (MB), '
                        'comment \n')
                f = open('%s.csv' % logfile, 'a+')
                f.write(header)
                f.close()

            # 获取内存使用情况（仅GPU）
            if use_gpu:
                mem = bm_matmul.get_memory_use()
            else:
                mem = 0

            # 写入测试结果
            with open('%s.csv' % logfile, 'a+') as f:
                f.write(gemm.generate_logtext(timeUsed, ops, mem))

        # 生成TensorFlow时间线（如果需要）
        if not args.no_timeline:
            bm_matmul.run_timeline(logfile, args.batchsize)
        print("\n========================================\n\n")


    # ==================== 卷积基准测试 ====================
    if args.testConv:
        # 设置日志文件路径
        if args.logfile == '':
            logfile = str('/results/benchmark_convolution_%s_%s'
                    % (args.device, time.strftime("%Y%m%d")))
        else:
            logfile = args.logfile

        # 计算卷积操作的总FLOP数
        ops = (args.batchsize                   # 批处理大小
                * args.matsize**2               # 输出特征图大小
                * (2*args.kernelsize**2         # 每个输出像素的操作数
                    * args.channels_in          # 输入通道数
                    * args.channels_out)        # 输出通道数
                )

        # 创建卷积基准测试对象
        conv = benchmark_conv_mult.convolution(args, devlist)

        # 根据参数选择不同的卷积实现方式
        if args.use_tf_layers:
            # 使用tf.layers实现
            conv_op, conv_graph = conv.create_benchmark_op1()
        else:
            # 使用原生TensorFlow操作
            conv_op, conv_graph = conv.create_benchmark_op2()

        # 创建基准测试运行器
        bm_conv = run_benchmark.benchmark(
                conv_op,                    # 卷积操作
                args.iter_warmup,           # 预热迭代次数
                args.iter_benchmark,        # 基准测试迭代次数
                args.iter_timeline,         # 时间线迭代次数
                conv_graph)                 # TensorFlow计算图

        print("========================================\n")
        print("开始卷积基准测试")

        # 运行基准测试
        timeUsed = bm_conv.run_benchmark()

        # 打印测试结果
        print("\n%d x %d x %d x %d 卷积 (float%d): "
                "%.3f ms, %.3f GFLOPS (%.2f 矩阵/秒)"
                % (args.matsize,            # 输入大小
                args.kernelsize,            # 卷积核大小
                args.channels_in,           # 输入通道数
                args.channels_out,          # 输出通道数
                args.precision,             # 精度
                timeUsed*1000,              # 执行时间（毫秒）
                ops*1e-9/timeUsed,          # GFLOPS性能
                1/timeUsed))                # 每秒处理数量

        # 保存卷积测试结果到CSV文件
        if not args.no_saving:
            # 如果CSV文件不存在，创建并写入表头
            if not os.path.isfile('%s.csv' % logfile):
                header = ('operation, matsize, batchsize, kernelsize, layers, '
                        'feature, precision (bits), time per run (ms), '
                        'performance (GFLOPs/sec), memory use (MB), comment\n')
                f = open('%s.csv' % logfile, 'a+')
                f.write(header)
                f.close()

            # 获取内存使用情况
            if use_gpu:
                mem = bm_conv.get_memory_use()
            else:
                mem = 0

            # 写入测试结果
            with open('%s.csv' % logfile, 'a+') as f:
                f.write(conv.generate_logtext(timeUsed, ops, mem))

        # 生成详细的时间线文件（如果需要）
        if not args.no_timeline:
            bm_conv.run_timeline(
                    '%s_%dx%dx%dx%d' % (logfile,
                                       args.matsize,        # 输入大小
                                       args.kernelsize,     # 卷积核大小
                                       args.channels_in,    # 输入通道数
                                       args.channels_out),  # 输出通道数
                    args.batchsize)

        print("\n========================================\n\n")


    # ==================== VGG16训练步骤基准测试 ====================
    if args.testVGG16:
        # 设置日志文件路径
        if args.logfile == '':
            logfile = str('/results/benchmark_VGG16_%s_%s'
                    % (args.device, time.strftime("%Y%m%d")))
        else:
            logfile = args.logfile

        # 创建VGG16模型对象
        model = benchmark_VGG16.VGG16(args)

        # 创建训练操作和计算图
        train_op, vgg16_graph = model.create_benchmark_op()

        # 创建基准测试运行器
        bm_vgg16 = run_benchmark.benchmark(
                train_op,                   # VGG16训练操作
                args.iter_warmup,           # 预热迭代次数
                args.iter_benchmark,        # 基准测试迭代次数
                args.iter_timeline,         # 时间线迭代次数
                vgg16_graph)                # VGG16计算图


        print("========================================\n")
        print("Start training VGG-16")
        timeUsed = bm_vgg16.run_benchmark()

        print("\nTraining VGG-16 (%dx%d pixel, float%d, batchsize %d): "
                "%.3f ms per batch / %.3f images per sec)"
                % (args.imgsize,
                args.imgsize,
                args.precision,
                args.batchsize,
                timeUsed*1000,
                args.batchsize/timeUsed))

        if not args.no_saving:
            if not os.path.isfile('%s.csv'%logfile):
                header = ('operation, imsize, precision (bits), batchsize,'
                        'time per batch (ms), performance (img/sec), '
                        'memory use (MB), comment\n')
                f = open('%s.csv'%logfile,'a+')
                f.write(header)
                f.close()

            if use_gpu:
                mem = bm_vgg16.get_memory_use()
            else:
                mem = 0
            with open('%s.csv'%logfile,'a+') as f:
                f.write(model.generate_logtext(timeUsed, mem))

        if not args.no_timeline:
            bm_vgg16.run_timeline(logfile, args.batchsize)
        print("\n========================================\n\n")

if __name__ == '__main__':
    tf.app.run()
