"""
TensorFlow时间线合并工具
用于合并多次运行的TensorFlow执行时间线，生成Chrome跟踪格式的性能分析文件

来源: https://github.com/ikhlestov/tensorflow_profiling.git
"""

import json


class TimeLiner:
    """
    时间线合并器类
    用于收集和合并多次TensorFlow运行的执行时间线数据
    """
    _timeline_dict = None  # 存储合并后的时间线数据

    def update_timeline(self, chrome_trace):
        """
        更新时间线数据

        参数:
            chrome_trace: Chrome跟踪格式的JSON字符串
        """
        # 将Chrome跟踪格式转换为Python字典
        chrome_trace_dict = json.loads(chrome_trace)

        # 第一次运行时存储完整的跟踪数据
        if self._timeline_dict is None:
            self._timeline_dict = chrome_trace_dict
        # 后续运行只更新时间消耗数据，不更新定义
        else:
            for event in chrome_trace_dict['traceEvents']:
                # 带有'ts'前缀的事件表示时间消耗数据
                if 'ts' in event:
                    self._timeline_dict['traceEvents'].append(event)

    def save(self, f_name):
        """
        保存合并后的时间线数据到文件

        参数:
            f_name: 输出文件名（JSON格式）
        """
        with open(f_name, 'w') as f:
            json.dump(self._timeline_dict, f)
