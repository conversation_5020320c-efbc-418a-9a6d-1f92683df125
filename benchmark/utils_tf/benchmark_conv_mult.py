"""
卷积基准测试模块
用于生成和执行2D卷积性能测试操作
"""

import tensorflow as tf


class convolution(object):
    """
    卷积基准测试类
    用于生成卷积操作的基准测试
    """

    def __init__(self, args, devlist):
        """
        初始化卷积基准测试对象

        参数:
            args: 输入参数对象，包含卷积配置
            devlist: GPU/CPU设备列表
        """
        self.matsize = args.matsize                    # 输入特征图大小
        self.kernelsize = args.kernelsize              # 卷积核大小
        self.channels_in = args.channels_in            # 输入通道数
        self.channels_out = args.channels_out          # 输出通道数
        self.batchsize = args.batchsize                # 批处理大小
        self.padding = args.padding                    # 填充方式（SAME/VALID）
        self.precision = args.precision                # 浮点精度
        self.comment = args.comment                    # 日志注释
        self.devlist = devlist                         # 设备列表
        self.interations_internal = args.iter_internal # 内部迭代次数

    def create_benchmark_op1(self):
        """
        使用tf.layers创建卷积基准测试操作

        返回:
            conv.op: 卷积操作
            g: TensorFlow计算图
        """
        # 根据精度设置数据类型
        datatype = eval('tf.float%d' % (self.precision))

        # 创建新的计算图
        g = tf.Graph()
        run_metadata = tf.RunMetadata()
        with g.as_default():
            # 为每个设备创建卷积操作
            for dev in self.devlist:
                with tf.device(dev):
                    # 创建输入张量（批大小 x 高 x 宽 x 通道数）
                    conv = tf.Variable(
                            tf.ones([
                                    self.batchsize,     # 批处理大小
                                    self.matsize,       # 特征图高度
                                    self.matsize,       # 特征图宽度
                                    self.channels_in],  # 输入通道数
                            dtype=datatype))

                    # 执行多次卷积操作（模拟深层网络）
                    for i in range(self.interations_internal):
                        conv = tf.layers.conv2d(
                                inputs=conv,                    # 输入张量
                                filters=self.channels_out,     # 输出通道数
                                kernel_size=self.kernelsize,   # 卷积核大小
                                padding=self.padding)          # 填充方式

        return conv.op, g

    def create_benchmark_op2(self):
        """
        使用tf.nn创建卷积基准测试操作

        返回:
            conv.op: 卷积操作
            g: TensorFlow计算图
        """
        # 根据精度设置数据类型
        datatype = eval('tf.float%d' % (self.precision))

        # 创建新的计算图
        g = tf.Graph()
        run_metadata = tf.RunMetadata()
        with g.as_default():
            # 为每个设备创建卷积操作
            for dev in self.devlist:
                with tf.device(dev):
                    # 创建输入张量（批大小 x 高 x 宽 x 通道数）
                    conv = tf.Variable(
                            tf.ones([
                                    self.batchsize,     # 批处理大小
                                    self.matsize,       # 特征图高度
                                    self.matsize,       # 特征图宽度
                                    self.channels_in],  # 输入通道数
                            dtype=datatype))

                    # 创建卷积核（高 x 宽 x 输入通道 x 输出通道）
                    kernel = tf.Variable(
                            tf.ones([
                                    self.kernelsize,    # 卷积核高度
                                    self.kernelsize,    # 卷积核宽度
                                    self.channels_in,   # 输入通道数
                                    self.channels_out], # 输出通道数
                            dtype=datatype))

                    # 执行多次卷积操作
                    for i in range(self.interations_internal):
                        conv = tf.nn.conv2d(
                                input=conv,             # 输入张量
                                filter=kernel,          # 卷积核
                                strides=[1,1,1,1],      # 步长 [batch, height, width, channels]
                                padding=self.padding)   # 填充方式

        return conv.op, g

    def generate_logtext(self, timeUsed, ops, mem):
        """
        生成日志文件的逗号分隔文本

        参数:
            timeUsed: 执行时间（秒）
            ops: 总操作数（FLOP）
            mem: 内存使用量（字节）

        返回:
            logtext: 格式化的日志文本
        """
        logtext = ('convolution, %d, %d, %d, %d, %d, %d, %.3f, %.3f, %.3f, %s\n'
                % (self.matsize,                # 输入大小
                self.batchsize,                 # 批处理大小
                self.kernelsize,                # 卷积核大小
                self.channels_in,               # 输入通道数
                self.channels_out,              # 输出通道数
                self.precision,                 # 精度
                timeUsed*1000,                  # 执行时间（毫秒）
                ops*1e-9/timeUsed,             # 性能（GFLOPS）
                mem/1e6,                       # 内存使用（MB）
                self.comment))                 # 注释

        return logtext
