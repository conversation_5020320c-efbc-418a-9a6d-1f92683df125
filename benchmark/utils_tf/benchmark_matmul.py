"""
矩阵乘法基准测试模块
用于生成和执行矩阵乘法性能测试操作
"""

import tensorflow as tf
from tensorflow.python.client import timeline
import numpy as np
import time
from utils_tf import merge_timeline

class gemm(object):
    """
    GEMM（通用矩阵乘法）基准测试类
    用于生成矩阵乘法基准测试操作
    """

    def __init__(self, args, devlist):
        """
        初始化GEMM基准测试对象

        参数:
            args: 输入参数对象，包含矩阵大小、精度等配置
            devlist: GPU/CPU设备列表
        """
        self.matsize = args.matsize        # 矩阵大小（N x N）
        self.precision = args.precision    # 浮点精度（16或32位）
        self.comment = args.comment        # 日志注释
        self.devlist = devlist            # 设备列表

    def create_benchmark_op(self):
        """
        创建矩阵乘法基准测试操作

        返回:
            prod.op: 矩阵乘法操作
            g: TensorFlow计算图
        """
        # 根据精度设置数据类型
        datatype = eval('tf.float%d' % (self.precision))

        # 创建新的计算图
        g = tf.Graph()
        with g.as_default():
            # 为每个设备创建矩阵乘法操作
            for dev in self.devlist:
                with tf.device(dev):
                    # 创建矩阵A（全1矩阵）
                    matA = tf.Variable(
                            tf.ones([self.matsize, self.matsize],
                            dtype=datatype))
                    # 创建矩阵B（全1矩阵）
                    matB = tf.Variable(
                            tf.ones([self.matsize, self.matsize],
                            dtype=datatype))
                    # 执行矩阵乘法 C = A * B
                    prod = tf.matmul(matA, matB)

        return prod.op, g

    def generate_logtext(self, timeUsed, ops, mem):
        """
        生成日志文件的逗号分隔文本

        参数:
            timeUsed: 执行时间（秒）
            ops: 总操作数（FLOP）
            mem: 内存使用量（字节）

        返回:
            logtext: 格式化的日志文本
        """
        logtext = ('matrix multiplication, %d, %d, %.3f, %.3f, %s\n'
                % (self.matsize,                    # 矩阵大小
                self.precision,                     # 精度
                ops*1e-9/timeUsed,                 # 性能（GFLOPS）
                mem/1e6,                           # 内存使用（MB）
                self.comment))                     # 注释
        return logtext
