"""
基准测试运行器模块
提供统一的基准测试执行框架，支持预热、性能测试、时间线生成和内存监控
"""
import tensorflow as tf
import time
from tensorflow.python.client import timeline
from utils_tf import merge_timeline


class benchmark(object):
    """
    基准测试类
    用于运行各种机器学习操作的性能基准测试
    """

    def __init__(self, benchmark_op, iterations_warmup,
                 iterations_benchmark, iterations_timeline, graph):
        """
        初始化基准测试对象

        参数:
            benchmark_op: TensorFlow张量，要在基准测试中执行的操作
            iterations_warmup: 预热阶段的迭代次数
            iterations_benchmark: 基准测试的迭代次数
            iterations_timeline: 生成时间线的迭代次数
            graph: TensorFlow计算图
        """
        self.benchmark_op = benchmark_op
        self.iterations_warmup = iterations_warmup
        self.iterations_benchmark = iterations_benchmark
        self.iterations_timeline = iterations_timeline
        self.graph = graph

        # 配置TensorFlow会话
        self.config = tf.ConfigProto(
                graph_options=tf.GraphOptions(
                        optimizer_options=tf.OptimizerOptions(
                                opt_level=tf.OptimizerOptions.L0)),  # 禁用优化以获得准确测试
                log_device_placement=False)  # 不记录设备放置信息


    def run_benchmark(self):
        """
        运行基准测试

        返回:
            timeUsed: 平均每次操作的执行时间（秒）
        """
        with tf.Session(config=self.config, graph=self.graph) as sess:
            # 初始化所有变量
            sess.run(tf.global_variables_initializer())

            # 预热阶段 - 避免冷启动对测试结果的影响
            for _ in range(self.iterations_warmup):
                sess.run(self.benchmark_op)

            # 正式基准测试阶段
            t = time.time()
            for _ in range(self.iterations_benchmark):
                sess.run(self.benchmark_op)
            timeUsed = (time.time() - t) / self.iterations_benchmark
        return timeUsed

    def run_timeline(self, logfile, batchsize):
        """
        运行基准测试并生成详细的执行时间线

        参数:
            logfile: 日志文件路径前缀
            batchsize: 批处理大小（用于文件命名）
        """
        run_metadata = tf.RunMetadata()
        with tf.Session(config=self.config, graph=self.graph) as sess:
            # 创建TensorBoard日志写入器
            train_writer = tf.summary.FileWriter('%s_tb' % logfile, self.graph)
            # 设置完整跟踪选项
            options = tf.RunOptions(trace_level=tf.RunOptions.FULL_TRACE)
            sess.run(tf.global_variables_initializer())

            # 预热阶段
            for _ in range(self.iterations_warmup):
                sess.run(self.benchmark_op, options=options, run_metadata=run_metadata)

            # 时间线生成阶段
            many_runs_timeline = merge_timeline.TimeLiner()
            t_start = time.time()
            for _ in range(self.iterations_timeline):
                # 执行操作并收集元数据
                sess.run(self.benchmark_op, options=options, run_metadata=run_metadata)
                # 生成Chrome跟踪格式的时间线
                fetched_timeline = timeline.Timeline(run_metadata.step_stats)
                chrome_trace = fetched_timeline.generate_chrome_trace_format()
                # 更新合并的时间线
                many_runs_timeline.update_timeline(chrome_trace)

            print("时间线运行时间: %.3f ms"
                    % ((time.time() - t_start) * 1000 / self.iterations_timeline))

            # 保存合并的时间线文件
            many_runs_timeline.save('%s_%dbatch_timeline.json' % (logfile, batchsize))

            # 添加运行元数据到TensorBoard
            train_writer.add_run_metadata(run_metadata, 'single convolution')
            train_writer.close()
        return

    def get_memory_use(self):
        """
        评估内存使用情况

        返回:
            mem: 最大内存使用量（字节）
        """
        with tf.Session(config=self.config, graph=self.graph) as sess:
            # 获取最大内存使用量
            mem = sess.run(tf.contrib.memory_stats.MaxBytesInUse())
        return mem
