"""
VGG16卷积神经网络训练基准测试模块
在合成数据上测试VGG16网络的训练性能
结果指标为每秒处理的图像数量
"""

import tensorflow as tf
from tensorflow.python.client import timeline
import tensorflow.contrib.slim as slim
import tensorflow.contrib.slim.nets as nets
import numpy as np
import time
from utils_tf import merge_timeline

class VGG16(object):
    """
    VGG16基准测试类
    用于生成VGG16网络训练的基准测试操作
    """

    def __init__(self, args):
        """
        初始化VGG16基准测试对象

        参数:
            args: 输入参数对象，包含网络配置
        """
        self.imgwidth = args.imgsize        # 图像宽度
        self.imgheight = args.imgsize       # 图像高度（正方形图像）
        self.numclasses = args.numclasses   # 分类类别数
        self.precision = args.precision     # 浮点精度
        self.batchsize = args.batchsize     # 批处理大小
        self.optimizer = args.optimizer     # 优化器类型
        self.devlist = args.devlist         # 设备列表
        self.comment = args.comment         # 日志注释


    def create_benchmark_op(self):
        """
        创建VGG16训练基准测试操作

        返回:
            train_op: VGG-16单步训练操作
            g: VGG-16计算图
        """
        # 生成合成数据
        datatype = eval('np.float%d' % (self.precision))

        # 创建新的计算图
        g = tf.Graph()
        with g.as_default():
            # 创建合成输入数据（避免I/O瓶颈）
            batch_data = np.zeros(
                    [self.batchsize,        # 批处理大小
                     self.imgwidth,         # 图像宽度
                     self.imgheight,        # 图像高度
                     3],                    # RGB通道数
                    dtype=datatype)

            # 创建合成标签（one-hot编码）
            batch_label = slim.one_hot_encoding(
                    np.zeros(self.batchsize, dtype=np.int32),  # 全零标签
                    self.numclasses)                           # 类别数

            # 从slim.nets.vgg定义VGG16模型
            model = nets.vgg.vgg_16

            # 通过模型运行数据，获得预测结果
            prediction, _ = model(
                    batch_data,                 # 输入数据
                    num_classes=self.numclasses) # 分类数量

            # 计算损失函数（softmax交叉熵）
            loss = tf.losses.softmax_cross_entropy(
                    batch_label,    # 真实标签
                    prediction)     # 预测结果

            # 定义优化器
            if self.optimizer == 'sgd':
                # 随机梯度下降优化器
                opt = tf.train.GradientDescentOptimizer(0.01)
            elif self.optimizer == 'rmsprop':
                # RMSprop优化器
                opt = tf.train.RMSPropOptimizer(0.001, 0.9)

            # 创建训练操作
            train_op = slim.learning.create_train_op(
                    loss,                       # 损失函数
                    optimizer=opt,              # 优化器
                    summarize_gradients=True)   # 记录梯度摘要
        return train_op, g

    def generate_logtext(self, timeUsed, mem):
        """
        生成日志文件的逗号分隔文本

        参数:
            timeUsed: 执行时间（秒）
            mem: 内存使用量（字节）

        返回:
            logtext: 格式化的日志文本
        """
        logtext = ('VGG-16, %d, %d, %d, %.3f, %.3f, %.3f, %s\n'
                % (self.imgwidth,               # 图像宽度
                self.precision,                 # 精度
                self.batchsize,                 # 批处理大小
                timeUsed*1000,                  # 执行时间（毫秒）
                self.batchsize/timeUsed,        # 性能（图像/秒）
                mem/1e6,                       # 内存使用（MB）
                self.comment))                 # 注释
        return logtext
