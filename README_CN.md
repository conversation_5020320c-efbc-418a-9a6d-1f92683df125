# 机器学习性能预测项目

## 项目简介

本项目是一个用于**预测深度学习模型计算成本**的机器学习工具。该项目可以训练机器学习模型来预测神经网络中常用层的执行时间，并通过组合这些预测来估算整个网络的性能。

这项工作旨在成为一个开源机器学习工具的起点，能够准确预测任何神经网络在任何给定硬件上所需的训练时间。因此，任何人都可以轻松添加额外的硬件、模型层或输入特征，或优化预测模型本身。

## 相关论文

本代码与论文 **"Predicting the Computational Cost of Deep Learning Models"** 相关。

## 相关项目

利用此模型进行推理的Python包可在以下地址找到：https://github.com/CDECatapult/mlpredict

## 项目结构

```
ml-performance-prediction/
├── benchmark/                    # 基准测试工具包
│   ├── benchmark.py             # 主要基准测试脚本
│   ├── utils_tf/                # TensorFlow工具模块
│   │   ├── run_benchmark.py     # 基准测试运行器
│   │   ├── benchmark_matmul.py  # 矩阵乘法基准测试
│   │   ├── benchmark_conv.py    # 卷积基准测试
│   │   ├── benchmark_VGG16.py   # VGG16网络基准测试
│   │   └── ...
│   ├── data/                    # 基准测试结果数据
│   ├── Dockerfile              # Docker容器配置
│   └── run_bm.sh               # 批量测试脚本
├── prediction_model/            # 预测模型
│   ├── model.py                # 神经网络模型定义
│   ├── dataprep.py             # 数据预处理模块
│   ├── Generate_train_data/     # 训练数据生成
│   │   ├── benchmark.py        # 训练数据生成器
│   │   ├── benchmark_conv.py   # 卷积层数据生成
│   │   ├── benchmark_dense.py  # 全连接层数据生成
│   │   └── ...
│   ├── data/                   # 训练数据存储
│   └── notebooks/              # Jupyter笔记本
├── README.md                   # 英文说明文档
└── README_CN.md               # 中文说明文档（本文件）
```

## 主要功能模块

### 📊 benchmark/ - 基准测试工具包

包含用于在不同硬件上基准测试深度学习操作的代码：

- **矩阵乘法基准测试**: 测试GEMM操作性能
- **2D卷积基准测试**: 测试卷积层性能
- **VGG16网络基准测试**: 测试完整CNN训练性能

### 🧠 prediction_model/ - 预测模型

包含生成训练数据、数据预处理管道和模型训练程序的代码：

- **模型训练**: 使用全连接神经网络预测执行时间
- **数据预处理**: 标准化和数据集分割
- **训练数据生成**: 自动生成大量基准测试样本

## 使用方法

### 环境要求

- Python 3.6+
- TensorFlow 1.x
- NumPy
- Pandas
- Scikit-learn
- NVIDIA GPU（推荐）

### 1. 基准测试

运行基准测试脚本：

```bash
cd benchmark
python benchmark.py [选项]
```

#### 基准测试选项：

**测试类型：**
- `--testMatMul`: 矩阵乘法基准测试
- `--testConv`: 2D卷积基准测试
- `--testVGG16`: VGG16训练基准测试

**通用参数：**
- `--num_gpu 1`: 使用的GPU数量
- `--precision 32`: 浮点精度（16或32位）
- `--iter_benchmark 200`: 基准测试迭代次数
- `--iter_warmup 10`: 预热迭代次数
- `--batchsize 128`: 批处理大小

**矩阵/卷积参数：**
- `--matsize 1024`: 矩阵大小
- `--kernelsize 3`: 卷积核大小
- `--channels_in 1`: 输入通道数
- `--channels_out 1`: 输出通道数

**VGG16参数：**
- `--imgsize 224`: 图像大小
- `--numclasses 1000`: 分类类别数
- `--optimizer sgd`: 优化器（sgd或rmsprop）

#### 示例命令：

```bash
# 矩阵乘法基准测试
python benchmark.py --testMatMul --matsize 2048 --precision 32

# 卷积基准测试
python benchmark.py --testConv --matsize 224 --kernelsize 3 --channels_in 64 --channels_out 128

# VGG16基准测试
python benchmark.py --testVGG16 --imgsize 224 --batchsize 32 --optimizer sgd
```

### 2. 训练数据生成

生成用于训练预测模型的数据：

```bash
cd prediction_model/Generate_train_data

# 生成卷积层训练数据
python benchmark.py --testConv --num_val 10000 --device GPU_NAME

# 生成全连接层训练数据
python benchmark.py --testDense --num_val 10000 --device GPU_NAME
```

### 3. 模型训练

使用Jupyter笔记本训练预测模型：

```bash
cd prediction_model/notebooks
jupyter notebook model_compTime.ipynb
```

### 4. Docker使用

构建和运行Docker容器：

```bash
# 构建基准测试容器
cd benchmark
sudo docker build -t mlbenchmark .

# 运行容器（需要NVIDIA Docker）
sudo docker run --runtime=nvidia -it --rm \
  --mount type=bind,source=/path/to/results,target=/results \
  mlbenchmark
```

## 支持的硬件

项目已在以下GPU上测试：

- NVIDIA V100
- NVIDIA P100
- NVIDIA K80
- NVIDIA K40
- NVIDIA M60
- NVIDIA GTX 1080Ti

## 输出数据格式

基准测试结果保存为CSV格式，包含以下信息：

- **操作类型**: 矩阵乘法、卷积、VGG16等
- **参数配置**: 大小、精度、批处理等
- **性能指标**: GFLOPS、执行时间、内存使用
- **硬件信息**: GPU型号、设备名称

## 工作流程

1. **数据收集**: 使用benchmark工具在不同硬件上运行基准测试
2. **训练数据生成**: 使用Generate_train_data生成大量训练样本
3. **数据预处理**: 使用dataprep.py进行标准化和分割
4. **模型训练**: 使用model.py和相关笔记本训练预测模型
5. **性能预测**: 使用训练好的模型预测新网络的执行时间

## 贡献指南

欢迎贡献代码！您可以：

- 添加新的硬件支持
- 实现新的网络层类型
- 优化预测模型架构
- 改进基准测试工具

## 许可证

请查看LICENSE文件了解许可证信息。

## 联系方式

如有问题或建议，请通过GitHub Issues联系我们。
